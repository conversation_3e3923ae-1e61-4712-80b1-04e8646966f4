"""
Test suite to verify the single API call strategy implementation.

This test ensures that:
1. ✅ Only one API call is made per plant/date selection
2. 💾 All data is cached in memory after the first call
3. 📊 All visualizations use cached data only
4. ⛔ No additional API calls during plot rendering
5. 🕵️‍♂️ Proper error handling and logging
"""

import pytest
import pandas as pd
from datetime import datetime
from unittest.mock import patch, MagicMock, call
import time

from backend.data.centralized_data_manager import CentralizedDataManager
from backend.data.centralized_data_wrapper import ensure_data_loaded

class TestSingleAPICallStrategy:
    """Test the single API call strategy implementation."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.manager = CentralizedDataManager()
        self.test_client = "KIDS Clinic"
        self.test_plant = "KIDS Clinic"
        self.test_date = datetime(2025, 6, 3)
        
    def test_single_api_call_per_plant(self):
        """Test that only one API call is made per plant."""
        with patch('backend.data.centralized_data_manager.get_generation_data_smart_wrapper') as mock_gen_api, \
             patch('backend.data.centralized_data_manager.get_consumption_data_from_csv') as mock_cons_api:
            
            # Mock API responses
            mock_gen_df = pd.DataFrame({
                'date': [self.test_date],
                'TOTAL_GENERATION': [100.0]
            })
            mock_cons_df = pd.DataFrame({
                'date': [self.test_date],
                'energy_kwh': [80.0]
            })
            
            mock_gen_api.return_value = mock_gen_df
            mock_cons_api.return_value = mock_cons_df
            
            # First call should trigger API
            success = self.manager.fetch_all_data(self.test_client, self.test_plant, 
                                                self.test_date, self.test_date)
            
            assert success == True
            assert mock_gen_api.call_count == 1
            assert mock_cons_api.call_count == 1
            
            # Second call with same parameters should NOT trigger API
            success2 = self.manager.fetch_all_data(self.test_client, self.test_plant, 
                                                 self.test_date, self.test_date)
            
            assert success2 == True
            # API should not be called again
            assert mock_gen_api.call_count == 1
            assert mock_cons_api.call_count == 1
    
    def test_data_cached_in_memory(self):
        """Test that data is properly cached in memory."""
        with patch('backend.data.centralized_data_manager.get_generation_data_smart_wrapper') as mock_gen_api, \
             patch('backend.data.centralized_data_manager.get_consumption_data_from_csv') as mock_cons_api:
            
            # Mock API responses
            mock_gen_df = pd.DataFrame({
                'date': [self.test_date],
                'TOTAL_GENERATION': [100.0]
            })
            mock_cons_df = pd.DataFrame({
                'date': [self.test_date],
                'energy_kwh': [80.0]
            })
            
            mock_gen_api.return_value = mock_gen_df
            mock_cons_api.return_value = mock_cons_df
            
            # Fetch data
            success = self.manager.fetch_all_data(self.test_client, self.test_plant, 
                                                self.test_date, self.test_date)
            
            assert success == True
            
            # Verify data is cached
            cached_gen = self.manager.get_generation_data(self.test_plant)
            cached_cons = self.manager.get_consumption_data(self.test_plant)
            cached_combined = self.manager.get_combined_data(self.test_plant)
            
            assert not cached_gen.empty
            assert not cached_cons.empty
            assert not cached_combined.empty
            
            # Verify data content
            assert len(cached_gen) == 1
            assert len(cached_cons) == 1
            assert cached_gen.iloc[0]['TOTAL_GENERATION'] == 100.0
            assert cached_cons.iloc[0]['energy_kwh'] == 80.0
    
    def test_no_api_calls_during_visualization(self):
        """Test that visualization functions don't trigger additional API calls."""
        with patch('backend.data.centralized_data_manager.get_generation_data_smart_wrapper') as mock_gen_api, \
             patch('backend.data.centralized_data_manager.get_consumption_data_from_csv') as mock_cons_api:
            
            # Mock API responses
            mock_gen_df = pd.DataFrame({
                'date': [self.test_date],
                'TOTAL_GENERATION': [100.0]
            })
            mock_cons_df = pd.DataFrame({
                'date': [self.test_date],
                'energy_kwh': [80.0]
            })
            
            mock_gen_api.return_value = mock_gen_df
            mock_cons_api.return_value = mock_cons_df
            
            # Initial data fetch
            success = self.manager.fetch_all_data(self.test_client, self.test_plant, 
                                                self.test_date, self.test_date)
            assert success == True
            
            initial_gen_calls = mock_gen_api.call_count
            initial_cons_calls = mock_cons_api.call_count
            
            # Simulate multiple visualization calls
            for _ in range(5):
                gen_data = self.manager.get_generation_data(self.test_plant)
                cons_data = self.manager.get_consumption_data(self.test_plant)
                combined_data = self.manager.get_combined_data(self.test_plant)
                tod_data = self.manager.get_tod_data(self.test_plant)
                
                assert not gen_data.empty
                assert not cons_data.empty
                assert not combined_data.empty
            
            # API should not be called again during visualizations
            assert mock_gen_api.call_count == initial_gen_calls
            assert mock_cons_api.call_count == initial_cons_calls
    
    def test_cache_invalidation_on_parameter_change(self):
        """Test that cache is invalidated when parameters change."""
        with patch('backend.data.centralized_data_manager.get_generation_data_smart_wrapper') as mock_gen_api, \
             patch('backend.data.centralized_data_manager.get_consumption_data_from_csv') as mock_cons_api:
            
            # Mock API responses
            mock_gen_df = pd.DataFrame({
                'date': [self.test_date],
                'TOTAL_GENERATION': [100.0]
            })
            mock_cons_df = pd.DataFrame({
                'date': [self.test_date],
                'energy_kwh': [80.0]
            })
            
            mock_gen_api.return_value = mock_gen_df
            mock_cons_api.return_value = mock_cons_df
            
            # First call
            success1 = self.manager.fetch_all_data(self.test_client, self.test_plant, 
                                                 self.test_date, self.test_date)
            assert success1 == True
            assert mock_gen_api.call_count == 1
            
            # Change date - should trigger new API call
            new_date = datetime(2025, 6, 4)
            success2 = self.manager.fetch_all_data(self.test_client, self.test_plant, 
                                                 new_date, new_date)
            assert success2 == True
            assert mock_gen_api.call_count == 2  # New API call triggered
    
    def test_error_handling_and_logging(self):
        """Test proper error handling and logging during API failures."""
        with patch('backend.data.centralized_data_manager.get_generation_data_smart_wrapper') as mock_gen_api, \
             patch('backend.data.centralized_data_manager.get_consumption_data_from_csv') as mock_cons_api, \
             patch('backend.data.centralized_data_manager.logger') as mock_logger:
            
            # Mock API failure
            mock_gen_api.side_effect = Exception("API Error")
            mock_cons_api.return_value = pd.DataFrame()
            
            # Attempt to fetch data
            success = self.manager.fetch_all_data(self.test_client, self.test_plant, 
                                                self.test_date, self.test_date)
            
            # Should handle error gracefully
            assert success == False
            
            # Verify error logging
            mock_logger.error.assert_called()
            error_calls = [call for call in mock_logger.error.call_args_list 
                          if "API ERROR" in str(call)]
            assert len(error_calls) > 0
    
    def test_performance_timing(self):
        """Test that performance timing is properly logged."""
        with patch('backend.data.centralized_data_manager.get_generation_data_smart_wrapper') as mock_gen_api, \
             patch('backend.data.centralized_data_manager.get_consumption_data_from_csv') as mock_cons_api, \
             patch('backend.data.centralized_data_manager.logger') as mock_logger:
            
            # Mock API responses with delay
            def slow_api_response(*args, **kwargs):
                time.sleep(0.1)  # Simulate API delay
                return pd.DataFrame({'date': [self.test_date], 'TOTAL_GENERATION': [100.0]})
            
            mock_gen_api.side_effect = slow_api_response
            mock_cons_api.return_value = pd.DataFrame({'date': [self.test_date], 'energy_kwh': [80.0]})
            
            # Fetch data
            start_time = time.time()
            success = self.manager.fetch_all_data(self.test_client, self.test_plant, 
                                                self.test_date, self.test_date)
            end_time = time.time()
            
            assert success == True
            
            # Verify timing logs
            timing_calls = [call for call in mock_logger.info.call_args_list 
                           if "completed in" in str(call)]
            assert len(timing_calls) > 0
    
    def test_ensure_data_loaded_wrapper(self):
        """Test the ensure_data_loaded wrapper function."""
        with patch.object(self.manager, 'fetch_all_data') as mock_fetch, \
             patch.object(self.manager, 'should_refresh_data') as mock_should_refresh, \
             patch.object(self.manager, 'is_data_available') as mock_is_available:
            
            # Mock successful data loading
            mock_should_refresh.return_value = True
            mock_fetch.return_value = True
            mock_is_available.return_value = True
            
            # Test wrapper function
            result = ensure_data_loaded(self.test_client, self.test_plant, 
                                      self.test_date, self.test_date)
            
            assert result == True
            mock_fetch.assert_called_once_with(self.test_client, self.test_plant, 
                                             self.test_date, self.test_date)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])

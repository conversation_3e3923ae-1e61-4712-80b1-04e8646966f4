2025-06-04 16:55:05,624 - INFO - centralized_data_manager - centralized_data_manager.py:60 - CentralizedDataManager initialized
2025-06-04 16:55:15,443 - INFO - centralized_data_manager - centralized_data_manager.py:60 - CentralizedDataManager initialized
2025-06-04 16:55:23,397 - INFO - centralized_data_manager - centralized_data_manager.py:60 - CentralizedDataManager initialized
2025-06-04 17:01:25,575 - INFO - centralized_data_manager - centralized_data_manager.py:60 - CentralizedDataManager initialized
2025-06-04 17:51:13,480 - INFO - centralized_data_manager - centralized_data_manager.py:60 - CentralizedDataManager initialized
2025-06-04 17:53:14,518 - INFO - centralized_data_manager - centralized_data_manager.py:60 - CentralizedDataManager initialized
2025-06-04 17:53:48,808 - INFO - centralized_data_manager - centralized_data_manager.py:60 - CentralizedDataManager initialized
2025-06-04 17:54:36,208 - INFO - centralized_data_manager - centralized_data_manager.py:60 - CentralizedDataManager initialized
2025-06-04 17:56:33,988 - INFO - centralized_data_manager - centralized_data_manager.py:60 - CentralizedDataManager initialized
2025-06-04 17:57:20,347 - INFO - centralized_data_manager - centralized_data_manager.py:60 - CentralizedDataManager initialized
2025-06-04 18:06:23,866 - INFO - centralized_data_manager - centralized_data_manager.py:62 - CentralizedDataManager initialized
2025-06-04 18:07:50,804 - INFO - centralized_data_manager - centralized_data_manager.py:62 - CentralizedDataManager initialized
2025-06-04 18:12:00,478 - INFO - centralized_data_manager - centralized_data_manager.py:62 - CentralizedDataManager initialized
2025-06-04 18:12:00,723 - INFO - centralized_data_manager - centralized_data_manager.py:303 - Data is up-to-date, using cached data (no API call needed)
2025-06-04 18:12:04,986 - INFO - centralized_data_manager - centralized_data_manager.py:303 - Data is up-to-date, using cached data (no API call needed)
2025-06-04 18:12:09,307 - INFO - centralized_data_manager - centralized_data_manager.py:62 - CentralizedDataManager initialized
2025-06-04 18:12:09,593 - INFO - centralized_data_manager - centralized_data_manager.py:303 - Data is up-to-date, using cached data (no API call needed)
2025-06-04 18:12:39,425 - INFO - centralized_data_manager - centralized_data_manager.py:62 - CentralizedDataManager initialized
2025-06-04 18:12:39,674 - INFO - centralized_data_manager - centralized_data_manager.py:303 - Data is up-to-date, using cached data (no API call needed)
2025-06-04 18:13:14,896 - INFO - centralized_data_manager - centralized_data_manager.py:62 - CentralizedDataManager initialized
2025-06-04 18:13:15,155 - INFO - centralized_data_manager - centralized_data_manager.py:303 - Data is up-to-date, using cached data (no API call needed)
2025-06-04 18:13:29,220 - INFO - centralized_data_manager - centralized_data_manager.py:62 - CentralizedDataManager initialized
2025-06-04 18:13:29,454 - INFO - centralized_data_manager - centralized_data_manager.py:303 - Data is up-to-date, using cached data (no API call needed)
2025-06-04 18:16:03,093 - INFO - centralized_data_manager - centralized_data_manager.py:62 - CentralizedDataManager initialized
2025-06-04 18:16:17,957 - INFO - centralized_data_manager - centralized_data_manager.py:62 - CentralizedDataManager initialized

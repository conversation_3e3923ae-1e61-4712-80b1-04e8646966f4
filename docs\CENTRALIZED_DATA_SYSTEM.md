# Centralized Data Management System

## Overview

The Centralized Data Management System is a new architecture that optimizes data fetching for the Energy Generation Dashboard. Instead of making multiple API calls for different plots, the system fetches all required data once and stores it in temporary DataFrames for reuse across all visualizations.

## Key Benefits

### 🚀 Performance Improvements
- **Single API Call**: Fetches all data once instead of multiple calls per plot
- **Parallel Processing**: Uses threading to fetch data for multiple plants simultaneously
- **Smart Caching**: Avoids redundant API calls when data is already available
- **Optimized Memory Usage**: Efficient data storage and retrieval

### 🔄 Better User Experience
- **Faster Loading**: Significantly reduced loading times for all tabs
- **Progress Indicators**: Clear feedback on data loading status
- **Cache Management**: Users can refresh data when needed
- **Data Availability Info**: Shows what data is available in the sidebar

### 🏗️ Improved Architecture
- **Centralized Logic**: All data fetching logic in one place
- **Thread Safety**: Safe concurrent access to cached data
- **Fallback Support**: Graceful fallback to original functions if needed
- **Easy Maintenance**: Simplified debugging and updates

## Architecture Components

### 1. CentralizedDataManager (`backend/data/centralized_data_manager.py`)

The core component that handles:
- **Data Fetching**: Parallel fetching of generation and consumption data
- **Data Processing**: Creating combined and ToD-binned datasets
- **Caching**: Thread-safe storage and retrieval of data
- **Metadata Management**: Tracking fetch timestamps and data availability

Key methods:
- `fetch_all_data()`: Main method to fetch all required data
- `get_generation_data()`: Access generation data for a plant
- `get_consumption_data()`: Access consumption data for a plant
- `get_combined_data()`: Access combined generation vs consumption data
- `get_tod_data()`: Access Time-of-Day binned data
- `get_combined_wind_solar_data()`: Access combined wind/solar data

### 2. CentralizedDataWrapper (`backend/data/centralized_data_wrapper.py`)

Wrapper functions that replace existing data fetching functions:
- `get_consumption_data_from_csv()`
- `get_generation_consumption_comparison()`
- `get_daily_consumption_data()`
- `get_daily_generation_consumption_comparison()`
- `get_combined_wind_solar_generation()`
- `get_tod_binned_data()`
- `get_banking_data()`

These functions maintain the same interface as the original functions but use the centralized data manager internally.

### 3. Application Integration (`app.py`)

The main application now:
1. **Loads data once** at the beginning using `ensure_data_loaded()`
2. **Shows progress** with a spinner during data loading
3. **Displays data summary** in the sidebar
4. **Provides cache management** controls for users

## Data Flow

```
User Selects Client/Plant/Date Range
           ↓
    ensure_data_loaded()
           ↓
  CentralizedDataManager.fetch_all_data()
           ↓
    Parallel Thread Execution:
    ├── fetch_generation_data_threaded()
    ├── fetch_consumption_data_threaded()
    └── process_tod_data()
           ↓
    Data stored in memory cache
           ↓
    Display functions use wrapper functions
           ↓
    Wrapper functions return cached data
           ↓
    Fast rendering of all plots
```

## Threading Architecture

The system uses separate threads for different data fetching operations:

### Summary Tab Thread
- Fetches generation data
- Fetches consumption data
- Creates combined datasets

### ToD Tab Thread
- Processes Time-of-Day binning
- Creates ToD-specific datasets

### Power Analysis Thread
- Processes banking calculations
- Creates cost analysis data

All threads run automatically in the background without requiring manual threading controls in the UI.

## Usage Examples

### Basic Usage
```python
from backend.data.centralized_data_manager import centralized_data_manager
from backend.data.centralized_data_wrapper import ensure_data_loaded

# Load all data for a client/plant/date range
success = ensure_data_loaded("Client Name", "Plant Name", start_date, end_date)

if success:
    # Get specific data types
    gen_data = centralized_data_manager.get_generation_data("Plant Name")
    cons_data = centralized_data_manager.get_consumption_data("Plant Name")
    combined_data = centralized_data_manager.get_combined_data("Plant Name")
    tod_data = centralized_data_manager.get_tod_data("Plant Name")
```

### Using Wrapper Functions (Recommended)
```python
from backend.data.centralized_data_wrapper import (
    get_consumption_data_from_csv,
    get_generation_consumption_comparison,
    get_tod_binned_data
)

# These functions now use the centralized data manager internally
consumption_df = get_consumption_data_from_csv("Plant Name", date)
gen_df, cons_df = get_generation_consumption_comparison("Plant Name", date)
tod_df = get_tod_binned_data("Plant Name", start_date, end_date)
```

## Configuration

The system respects existing configuration settings:

```python
# In backend/config/app_config.py
CONFIG = {
    "data": {
        "enable_smart_caching": True,
        "max_concurrent_requests": 4,
        "cache_ttl": 3600,
        # ... other settings
    }
}
```

## Cache Management

### Automatic Cache Management
- Data is automatically cached for 5 minutes
- Cache is invalidated when client/plant/date selection changes
- Thread-safe access to cached data

### Manual Cache Management
Users can:
- **Refresh Data**: Force reload all data (🔄 button in sidebar)
- **Clear Cache**: Remove all cached data (🗑️ button in sidebar)

### Cache Information
The sidebar shows:
- ✅ Data load timestamp
- 📊 Available data types and plant counts
- Cache management controls

## Error Handling

The system includes comprehensive error handling:

1. **Graceful Fallbacks**: If centralized data is not available, functions fall back to original implementations
2. **Error Logging**: All errors are logged with detailed context
3. **User Feedback**: Clear error messages and recovery suggestions
4. **Thread Safety**: Proper locking prevents data corruption

## Performance Metrics

Expected performance improvements:
- **Loading Time**: 60-80% reduction in initial load time
- **API Calls**: 70-90% reduction in total API calls
- **Memory Usage**: Optimized data storage and reuse
- **User Experience**: Smoother navigation between tabs

## Migration Guide

### For Developers

The migration is largely transparent:

1. **Display Functions**: No changes needed - they continue to use the same function names
2. **Import Changes**: Functions now imported from `centralized_data_wrapper` instead of `data`
3. **New Features**: Access to centralized data manager for advanced use cases

### For Users

Users will notice:
1. **Faster Loading**: Significantly improved performance
2. **Better Feedback**: Progress indicators and data availability info
3. **Cache Controls**: Ability to refresh data when needed

## Testing

Run the test suite:
```bash
python -m pytest tests/test_centralized_data_manager.py -v
```

The tests cover:
- Data manager initialization
- Thread-safe data access
- Cache management
- Error handling
- Wrapper function compatibility

## Future Enhancements

Planned improvements:
1. **Persistent Caching**: Save cache to disk for faster startup
2. **Incremental Updates**: Update only changed data
3. **Background Refresh**: Automatic data refresh in background
4. **Advanced Analytics**: Cache hit/miss statistics
5. **Data Compression**: Reduce memory usage for large datasets

## Troubleshooting

### Common Issues

**Data not loading:**
- Check network connectivity
- Verify API credentials
- Use "Refresh Data" button
- Check browser console for errors

**Performance issues:**
- Clear cache and reload
- Check available memory
- Reduce date range if needed

**Cache problems:**
- Use "Clear Cache" button
- Restart the application
- Check file permissions in cache directory

### Debug Mode

Enable debug logging:
```python
import logging
logging.getLogger('centralized_data_manager').setLevel(logging.DEBUG)
```

This will provide detailed information about:
- Data fetching operations
- Cache hits and misses
- Thread execution
- Error details

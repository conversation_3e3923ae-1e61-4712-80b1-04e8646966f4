"""
Main Streamlit application entry point for the Energy Generation Dashboard.
"""
import streamlit as st
from dotenv import load_dotenv
import traceback
import time

from backend.config.app_config import setup_page
from frontend.components.ui_components import create_client_plant_filters, create_date_filters
from backend.data.centralized_data_manager import centralized_data_manager
from backend.data.centralized_data_wrapper import ensure_data_loaded
from src.display_components import (
    display_consumption_view,
    display_generation_consumption_view, display_daily_generation_consumption_view,
    display_daily_consumption_view,
    display_combined_wind_solar_view,
    display_tod_binned_view,
    display_daily_tod_binned_view, display_tod_generation_view,
    display_tod_consumption_view,
    display_generation_only_view
)

# Configure logging
from backend.logs.logger_setup import setup_logger

logger = setup_logger('app', 'app.log')

# Load environment variables
load_dotenv()

def main():
    """Main application function."""
    try:
        # Setup page configuration
        setup_page()

        # Create UI components
        selected_client, selected_plant, has_solar, has_wind = create_client_plant_filters()
        start_date, end_date = create_date_filters()

        # Validate selections
        if not selected_client:
            st.warning("Please select a client to continue.")
            return

        if not selected_plant:
            st.warning("Please select a plant to continue.")
            return

        # Check if single day is selected
        is_single_day = start_date == end_date

        # SINGLE API CALL STRATEGY: Load all data once
        data_load_start = time.time()
        
        with st.spinner("Fetching all data via single API call strategy..."):
            # Check if data is already cached
            if centralized_data_manager.is_data_available():
                data_summary = centralized_data_manager.get_data_summary()
                if (data_summary['selected_client'] == selected_client and 
                    data_summary['date_range'] and
                    data_summary['date_range'][0].date() == start_date.date() and
                    data_summary['date_range'][1].date() == end_date.date()):
                    st.sidebar.success("Using cached data (no API call needed)")
                    data_loaded = True
                else:
                    data_loaded = ensure_data_loaded(selected_client, selected_plant, start_date, end_date)
            else:
                data_loaded = ensure_data_loaded(selected_client, selected_plant, start_date, end_date)
            
            data_load_duration = time.time() - data_load_start
            
            if not data_loaded:
                st.error("Failed to load data. Please check your selections and try again.")
                st.info("Troubleshooting tips:")
                st.info("• Check your internet connection")
                st.info("• Verify the selected plant has data for the chosen date")
                st.info("• Try refreshing the data using the button below")
                st.info("• If the problem persists, please contact support.")
                
                # Show refresh button even on failure
                if st.button("Try Again", help="Retry data loading"):
                    centralized_data_manager.clear_cache()
                    st.rerun()
                return
            
            # Show data summary in sidebar
            data_summary = centralized_data_manager.get_data_summary()
            if data_summary['fetch_timestamp']:
                st.sidebar.success(f"Data loaded at {data_summary['fetch_timestamp'].strftime('%H:%M:%S')} ({data_load_duration:.2f}s)")
                
                # Show available data types
                available_data = []
                if data_summary['generation_plants']:
                    available_data.append(f"Generation ({len(data_summary['generation_plants'])} plants)")
                if data_summary['consumption_plants']:
                    available_data.append(f"Consumption ({len(data_summary['consumption_plants'])} plants)")
                if data_summary['tod_plants']:
                    available_data.append(f"ToD Analysis ({len(data_summary['tod_plants'])} plants)")
                
                if available_data:
                    st.sidebar.info("**Cached Data Available:**\n" + "\n".join([f"• {data}" for data in available_data]))
                    st.sidebar.info("**No additional API calls** will be made for visualizations")
                
                # Add cache management controls
                st.sidebar.markdown("---")
                st.sidebar.markdown("**Cache Management**")
                
                col1, col2 = st.sidebar.columns(2)
                with col1:
                    if st.button("Refresh Data", help="Force refresh all data"):
                        centralized_data_manager.clear_cache()
                        st.rerun()
                
                with col2:
                    if st.button("Clear Cache", help="Clear all cached data"):
                        centralized_data_manager.clear_cache()
                        st.success("Cache cleared!")
                        
                # Show performance metrics
                st.sidebar.markdown("---")
                st.sidebar.markdown("**Performance Metrics**")
                st.sidebar.metric("Data Load Time", f"{data_load_duration:.2f}s")
                st.sidebar.metric("Cache Status", "Active" if data_summary['fetch_timestamp'] else "Empty")

        # Create tabs for Summary, ToD, and Power Cost Analysis
        summary_tab, tod_tab, cost_tab = st.tabs(["Summary", "ToD", "Power Cost Analysis"])


        # Summary Tab Content
        with summary_tab:
            # Show Generation vs Consumption based on date selection (1st plot)
            st.header("Generation vs Consumption")
            if is_single_day:
                display_generation_consumption_view(selected_plant, start_date, section="summary")
            else:
                display_daily_generation_consumption_view(selected_plant, start_date, end_date, section="summary")

            # Show generation plots as 2nd plot based on client configuration and plant selection
            if has_solar and has_wind:
                # Client has both wind and solar plants
                st.header("Combined Wind and Solar Generation")
                display_combined_wind_solar_view(selected_client, start_date, end_date, section="summary")
            elif selected_plant != "Combined View":
                # Client has only one plant OR user selected a specific plant
                st.header("Generation")
                display_generation_only_view(selected_plant, start_date, end_date, section="summary")
            else:
                # This case shouldn't occur, but handle it gracefully
                st.info("Please select a specific plant to view the Generation plot.")

            # Show Consumption based on date selection (3rd plot)
            st.header("Consumption")
            if is_single_day:
                display_consumption_view(selected_plant, start_date, section="summary")
            else:
                display_daily_consumption_view(selected_plant, start_date, end_date, section="summary")

        # ToD Tab Content
        with tod_tab:

            # Show ToD Generation vs Consumption with custom time bins (as the first plot)
            st.header("ToD Generation vs Consumption")

            # Only show the ToD binned view if we have a specific plant selected (not "Combined View")
            if selected_plant != "Combined View":
                # For single day view
                if is_single_day:
                    display_tod_binned_view(selected_plant, start_date, end_date, section="tod")
                # For multi-day view, use the daily ToD binned plot
                else:
                    display_daily_tod_binned_view(selected_plant, start_date, end_date, section="tod")
            else:
                st.info("Please select a specific plant to view the ToD Generation vs Consumption comparison.")

            # Show ToD Generation
            st.header("ToD Generation")
            if selected_plant != "Combined View":
                display_tod_generation_view(selected_plant, start_date, end_date, section="tod")
            else:
                st.info("Please select a specific plant to view the ToD Generation.")

            # Show ToD Consumption
            st.header("ToD Consumption")
            if selected_plant != "Combined View":
                display_tod_consumption_view(selected_plant, start_date, end_date, section="tod")
            else:
                st.info("Please select a specific plant to view the ToD Consumption.")

        # Power Cost Analysis Tab Content
        with cost_tab:
            st.header("Power Cost Analysis")

            if selected_plant != "Combined View":
                from src.display_components import display_power_cost_analysis
                display_power_cost_analysis(selected_plant, start_date, end_date, is_single_day)
            else:
                st.info("Please select a specific plant to view the Power Cost Analysis.")

    except Exception as e:
        logger.error(f"Application error: {e}")
        logger.error(traceback.format_exc())
        st.error("An unexpected error occurred. Please try again later.")
        st.error(f"Error details: {str(e)}")
        st.info("If this problem persists, please contact support.")

if __name__ == "__main__":
    main()

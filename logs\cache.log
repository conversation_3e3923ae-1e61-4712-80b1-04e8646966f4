2025-05-27 15:48:35,600 - INFO - cache_service - cache_service.py:45 - Cache service initialized with TTL: 21600s
2025-05-27 15:51:44,802 - INFO - cache_service - cache_service.py:45 - Cache service initialized with TTL: 21600s
2025-05-27 15:53:28,797 - INFO - cache_service - cache_service.py:45 - Cache service initialized with TTL: 21600s
2025-05-27 15:53:29,884 - INFO - cache_service - cache_service.py:217 - Initializing cache system...
2025-05-27 15:53:29,891 - ERROR - cache_service - cache_service.py:230 - Cache system initialization failed: 'timestamp'
2025-05-27 15:55:04,803 - INFO - cache_service - cache_service.py:217 - Initializing cache system...
2025-05-27 15:55:04,804 - ERROR - cache_service - cache_service.py:230 - Cache system initialization failed: 'timestamp'
2025-05-27 15:55:10,396 - INFO - cache_service - cache_service.py:217 - Initializing cache system...
2025-05-27 15:55:10,396 - ERROR - cache_service - cache_service.py:230 - Cache system initialization failed: 'timestamp'
2025-05-27 15:55:11,113 - INFO - cache_service - cache_service.py:217 - Initializing cache system...
2025-05-27 15:55:11,114 - ERROR - cache_service - cache_service.py:230 - Cache system initialization failed: 'timestamp'
2025-05-27 15:55:29,235 - INFO - cache_service - cache_service.py:45 - Cache service initialized with TTL: 21600s
2025-05-27 15:55:29,985 - INFO - cache_service - cache_service.py:217 - Initializing cache system...
2025-05-27 15:55:29,986 - ERROR - cache_service - cache_service.py:230 - Cache system initialization failed: 'timestamp'
2025-05-27 16:17:52,861 - INFO - cache_service - cache_service.py:45 - Cache service initialized with TTL: 21600s
2025-05-27 16:17:52,877 - INFO - cache_service - cache_service.py:217 - Initializing cache system...
2025-05-27 16:17:52,878 - ERROR - cache_service - cache_service.py:230 - Cache system initialization failed: 'timestamp'
2025-05-27 16:18:02,822 - INFO - cache_service - cache_service.py:217 - Initializing cache system...
2025-05-27 16:18:02,822 - ERROR - cache_service - cache_service.py:230 - Cache system initialization failed: 'timestamp'
2025-05-27 16:18:13,252 - INFO - cache_service - cache_service.py:45 - Cache service initialized with TTL: 21600s
2025-05-27 16:18:13,903 - INFO - cache_service - cache_service.py:217 - Initializing cache system...
2025-05-27 16:18:13,903 - ERROR - cache_service - cache_service.py:230 - Cache system initialization failed: 'timestamp'
2025-05-27 16:18:20,135 - INFO - cache_service - cache_service.py:217 - Initializing cache system...
2025-05-27 16:18:20,136 - ERROR - cache_service - cache_service.py:230 - Cache system initialization failed: 'timestamp'
2025-05-27 16:19:19,755 - INFO - cache_service - cache_service.py:45 - Cache service initialized with TTL: 21600s
2025-05-27 16:20:46,761 - INFO - cache_service - cache_service.py:45 - Cache service initialized with TTL: 21600s
2025-05-27 16:21:28,899 - INFO - cache_service - cache_service.py:45 - Cache service initialized with TTL: 21600s
2025-05-27 16:21:37,916 - INFO - cache_service - cache_service.py:45 - Cache service initialized with TTL: 21600s
2025-05-27 16:23:30,410 - INFO - cache_service - cache_service.py:45 - Cache service initialized with TTL: 21600s
2025-05-27 16:23:30,433 - INFO - cache_service - cache_service.py:217 - Initializing cache system...
2025-05-27 16:23:30,433 - ERROR - cache_service - cache_service.py:230 - Cache system initialization failed: 'timestamp'
2025-05-27 16:29:09,571 - INFO - cache_service - cache_service.py:217 - Initializing cache system...
2025-05-27 16:29:09,572 - ERROR - cache_service - cache_service.py:230 - Cache system initialization failed: 'timestamp'
2025-05-27 16:29:18,753 - INFO - cache_service - cache_service.py:217 - Initializing cache system...
2025-05-27 16:29:18,754 - ERROR - cache_service - cache_service.py:230 - Cache system initialization failed: 'timestamp'
2025-05-27 16:29:18,951 - INFO - cache_service - cache_service.py:217 - Initializing cache system...
2025-05-27 16:29:18,951 - ERROR - cache_service - cache_service.py:230 - Cache system initialization failed: 'timestamp'
2025-05-27 16:39:04,329 - INFO - cache_service - cache_service.py:45 - Cache service initialized with TTL: 21600s
2025-05-27 16:39:51,362 - INFO - cache_service - cache_service.py:45 - Cache service initialized with TTL: 21600s
2025-05-27 16:40:30,197 - INFO - cache_service - cache_service.py:45 - Cache service initialized with TTL: 21600s
2025-05-27 16:44:38,216 - INFO - cache_service - cache_service.py:45 - Cache service initialized with TTL: 21600s
2025-05-27 16:44:38,249 - INFO - cache_service - cache_service.py:290 - Initializing cache system...
2025-05-27 16:44:38,250 - WARNING - cache_service - cache_service.py:164 - Cache entry last_bulk_fetch missing timestamp, marking for removal
2025-05-27 16:44:38,250 - WARNING - cache_service - cache_service.py:164 - Cache entry cache_entries missing timestamp, marking for removal
2025-05-27 16:44:38,250 - WARNING - cache_service - cache_service.py:164 - Cache entry version missing timestamp, marking for removal
2025-05-27 16:44:38,251 - INFO - cache_service - cache_service.py:187 - Cleaned up 3 old cache entries
2025-05-27 16:44:38,252 - INFO - cache_service - cache_service.py:297 - Cache initialized: 0 entries, 0.00 MB
2025-05-27 16:44:38,252 - INFO - cache_service - cache_service.py:300 - Cache system initialization completed
2025-05-27 16:46:35,623 - INFO - cache_service - cache_service.py:45 - Cache service initialized with TTL: 21600s
2025-05-27 16:48:10,307 - INFO - cache_service - cache_service.py:290 - Initializing cache system...
2025-05-27 16:48:10,307 - INFO - cache_service - cache_service.py:297 - Cache initialized: 0 entries, 0.00 MB
2025-05-27 16:48:10,307 - INFO - cache_service - cache_service.py:300 - Cache system initialization completed
2025-05-27 16:48:37,475 - INFO - cache_service - cache_service.py:45 - Cache service initialized with TTL: 21600s
2025-05-27 16:48:38,096 - INFO - cache_service - cache_service.py:290 - Initializing cache system...
2025-05-27 16:48:38,096 - INFO - cache_service - cache_service.py:297 - Cache initialized: 0 entries, 0.00 MB
2025-05-27 16:48:38,096 - INFO - cache_service - cache_service.py:300 - Cache system initialization completed

2025-05-27 12:54:54,363 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-05-27 12:56:06,727 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-05-27 12:56:27,524 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for IN.INTE.KIDS generation, fetching from API
2025-05-27 12:56:37,871 - INFO - api_cache_manager - api_cache_manager.py:190 - Cached data for IN.INTE.KIDS generation (768 rows)
2025-05-27 12:56:38,149 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-19 to 2025-05-26)
2025-05-27 12:56:38,421 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for IN.INTE.KIDS generation, fetching from API
2025-05-27 12:56:48,749 - INFO - api_cache_manager - api_cache_manager.py:190 - Cached data for IN.INTE.KIDS generation (24 rows)
2025-05-27 12:56:49,014 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-26 to 2025-05-26)
2025-05-27 12:56:49,276 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for IN.INTE.KIDS generation, fetching from API
2025-05-27 12:56:59,816 - INFO - api_cache_manager - api_cache_manager.py:190 - Cached data for IN.INTE.KIDS generation (24 rows)
2025-05-27 12:57:00,077 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for IN.INTE.KIDS generation, fetching from API
2025-05-27 12:57:10,304 - INFO - api_cache_manager - api_cache_manager.py:190 - Cached data for IN.INTE.KIDS generation (24 rows)
2025-05-27 12:57:10,565 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for IN.INTE.KIDS generation, fetching from API
2025-05-27 12:57:20,552 - INFO - api_cache_manager - api_cache_manager.py:190 - Cached data for IN.INTE.KIDS generation (24 rows)
2025-05-27 12:57:20,828 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for IN.INTE.KIDS generation, fetching from API
2025-05-27 12:57:30,986 - INFO - api_cache_manager - api_cache_manager.py:190 - Cached data for IN.INTE.KIDS generation (24 rows)
2025-05-27 12:57:31,250 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for IN.INTE.KIDS generation, fetching from API
2025-05-27 12:57:41,420 - INFO - api_cache_manager - api_cache_manager.py:190 - Cached data for IN.INTE.KIDS generation (24 rows)
2025-05-27 12:57:41,689 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for IN.INTE.KIDS generation, fetching from API
2025-05-27 12:57:52,123 - INFO - api_cache_manager - api_cache_manager.py:190 - Cached data for IN.INTE.KIDS generation (24 rows)
2025-05-27 12:57:52,386 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for IN.INTE.KIDS generation, fetching from API
2025-05-27 12:58:02,587 - INFO - api_cache_manager - api_cache_manager.py:190 - Cached data for IN.INTE.KIDS generation (24 rows)
2025-05-27 12:58:02,849 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-26 to 2025-05-26)
2025-05-27 12:58:03,118 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-19 to 2025-05-19)
2025-05-27 12:58:03,377 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-20 to 2025-05-20)
2025-05-27 12:58:03,637 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-21 to 2025-05-21)
2025-05-27 12:58:03,896 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-22 to 2025-05-22)
2025-05-27 12:58:04,151 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-23 to 2025-05-23)
2025-05-27 12:58:04,405 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-24 to 2025-05-24)
2025-05-27 12:58:04,660 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-25 to 2025-05-25)
2025-05-27 12:58:04,925 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-26 to 2025-05-26)
2025-05-27 12:58:05,185 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-19 to 2025-05-26)
2025-05-27 12:58:05,533 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-19 to 2025-05-26)
2025-05-27 13:04:06,014 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-05-27 13:04:27,710 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-19 to 2025-05-26)
2025-05-27 13:04:28,298 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-19 to 2025-05-26)
2025-05-27 13:04:28,860 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-26 to 2025-05-26)
2025-05-27 13:04:29,314 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-26 to 2025-05-26)
2025-05-27 13:04:29,801 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-19 to 2025-05-19)
2025-05-27 13:04:30,388 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-20 to 2025-05-20)
2025-05-27 13:04:30,802 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-21 to 2025-05-21)
2025-05-27 13:04:31,420 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-22 to 2025-05-22)
2025-05-27 13:04:31,935 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-23 to 2025-05-23)
2025-05-27 13:04:32,443 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-24 to 2025-05-24)
2025-05-27 13:04:33,024 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-25 to 2025-05-25)
2025-05-27 13:04:33,490 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-26 to 2025-05-26)
2025-05-27 13:04:34,038 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-19 to 2025-05-19)
2025-05-27 13:04:34,322 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-20 to 2025-05-20)
2025-05-27 13:04:34,594 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-21 to 2025-05-21)
2025-05-27 13:04:34,874 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-22 to 2025-05-22)
2025-05-27 13:04:35,141 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-23 to 2025-05-23)
2025-05-27 13:04:35,424 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-24 to 2025-05-24)
2025-05-27 13:04:35,715 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-25 to 2025-05-25)
2025-05-27 13:04:36,029 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-26 to 2025-05-26)
2025-05-27 13:04:36,437 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-19 to 2025-05-26)
2025-05-27 13:04:36,772 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-19 to 2025-05-26)
2025-05-27 13:10:33,745 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-05-27 13:10:33,955 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-19 to 2025-05-26)
2025-05-27 13:16:28,858 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-05-27 13:16:29,079 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for IN.INTE.KIDS generation, fetching from API
2025-05-27 13:16:39,309 - INFO - api_cache_manager - api_cache_manager.py:190 - Cached data for IN.INTE.KIDS generation (384 rows)
2025-05-27 13:17:08,371 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-05-27 13:17:08,588 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for IN.INTE.KIDS generation, fetching from API
2025-05-27 13:17:18,491 - INFO - api_cache_manager - api_cache_manager.py:190 - Cached data for IN.INTE.KIDS generation (96 rows)
2025-05-27 13:17:31,133 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-05-27 13:17:51,922 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-19 to 2025-05-26)
2025-05-27 13:17:52,215 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-19 to 2025-05-26)
2025-05-27 13:17:52,477 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-26 to 2025-05-26)
2025-05-27 13:17:52,751 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-26 to 2025-05-26)
2025-05-27 13:17:53,030 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-19 to 2025-05-19)
2025-05-27 13:17:53,348 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-20 to 2025-05-20)
2025-05-27 13:17:53,664 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-21 to 2025-05-21)
2025-05-27 13:17:53,938 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-22 to 2025-05-22)
2025-05-27 13:17:54,209 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-23 to 2025-05-23)
2025-05-27 13:17:54,476 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-24 to 2025-05-24)
2025-05-27 13:17:54,737 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-25 to 2025-05-25)
2025-05-27 13:17:55,009 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-26 to 2025-05-26)
2025-05-27 13:17:55,280 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-19 to 2025-05-19)
2025-05-27 13:17:55,546 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-20 to 2025-05-20)
2025-05-27 13:17:55,812 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-21 to 2025-05-21)
2025-05-27 13:17:56,082 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-22 to 2025-05-22)
2025-05-27 13:17:56,348 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-23 to 2025-05-23)
2025-05-27 13:17:56,611 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-24 to 2025-05-24)
2025-05-27 13:17:56,879 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-25 to 2025-05-25)
2025-05-27 13:17:57,157 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-26 to 2025-05-26)
2025-05-27 13:17:57,437 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-19 to 2025-05-26)
2025-05-27 13:17:57,691 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-19 to 2025-05-26)
2025-05-27 13:24:50,327 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-05-27 13:24:50,538 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for ANS Paper Mills Pvt Ltd generation, fetching from API
2025-05-27 13:25:30,020 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-05-27 13:25:30,245 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-05-26 to 2025-05-26)
2025-05-27 13:26:08,782 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-05-27 13:26:09,007 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for IN.INTE.KIDS generation, fetching from API
2025-05-27 13:26:19,127 - INFO - api_cache_manager - api_cache_manager.py:190 - Cached data for IN.INTE.KIDS generation (96 rows)
2025-05-27 14:24:41,896 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-05-27 14:24:42,145 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for IN.INTE.KIDS generation, fetching from API
2025-05-27 14:25:07,798 - INFO - api_cache_manager - api_cache_manager.py:190 - Cached data for IN.INTE.KIDS generation (384 rows)
2025-05-27 14:25:49,722 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-05-27 14:25:49,950 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-01-02 to 2025-01-05)
2025-05-27 14:36:49,227 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-05-27 14:36:49,435 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for IN.INTE.KIDS generation, fetching from API
2025-05-27 14:37:04,381 - INFO - api_cache_manager - api_cache_manager.py:190 - Cached data for IN.INTE.KIDS generation (192 rows)
2025-05-27 14:41:08,255 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-05-27 14:41:08,465 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for Test Plant generation, fetching from API
2025-05-27 14:41:09,549 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for Sample Plant generation, fetching from API
2025-05-27 14:41:10,636 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for Demo Plant generation, fetching from API
2025-05-27 14:41:51,360 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-05-27 14:41:51,584 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for IN.INTE.KIDS generation, fetching from API
2025-05-27 14:42:24,485 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for IN.INTE.SKFT generation, fetching from API
2025-05-27 14:42:42,222 - INFO - api_cache_manager - api_cache_manager.py:190 - Cached data for IN.INTE.SKFT generation (672 rows)
2025-05-27 14:42:42,562 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for IN.INTE.ANS1 generation, fetching from API
2025-05-27 14:43:11,701 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-05-27 14:43:11,911 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for IN.INTE.KIDS generation, fetching from API
2025-05-27 14:43:27,426 - INFO - api_cache_manager - api_cache_manager.py:190 - Cached data for IN.INTE.KIDS generation (672 rows)
2025-05-27 14:46:15,152 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-05-27 14:46:15,369 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-04-02 to 2025-04-02)
2025-05-27 14:48:17,347 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-05-27 14:48:17,580 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for IN.INTE.KIDS generation, fetching from API
2025-05-27 14:48:37,277 - INFO - api_cache_manager - api_cache_manager.py:190 - Cached data for IN.INTE.KIDS generation (1 rows)
2025-05-28 11:25:32,665 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-05-28 11:26:11,086 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-05-28 11:26:11,321 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for IN.INTE.ANS1 generation, fetching from API
2025-05-28 11:26:24,245 - INFO - api_cache_manager - api_cache_manager.py:190 - Cached data for IN.INTE.ANS1 generation (24 rows)
2025-06-04 12:44:55,318 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-06-04 12:44:55,535 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for ANS Paper Mills Pvt Ltd generation, fetching from API
2025-06-04 12:44:56,577 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for ANS Paper Mills Pvt Ltd generation, fetching from API
2025-06-04 12:45:45,689 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-06-04 12:45:45,908 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for IN.INTE.KIDS generation, fetching from API
2025-06-04 12:46:17,231 - INFO - api_cache_manager - api_cache_manager.py:190 - Cached data for IN.INTE.KIDS generation (72 rows)
2025-06-04 12:46:17,731 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2024-12-01 to 2024-12-03)
2025-06-04 12:47:26,468 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-06-04 12:47:26,704 - INFO - api_cache_manager - api_cache_manager.py:353 - Cache miss for IN.INTE.KIDS generation, fetching from API
2025-06-04 12:47:58,813 - INFO - api_cache_manager - api_cache_manager.py:190 - Cached data for IN.INTE.KIDS generation (72 rows)
2025-06-04 12:47:59,425 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-01-01 to 2025-01-03)
2025-06-04 12:49:55,983 - INFO - api_cache_manager - api_cache_manager.py:52 - API Cache Manager initialized with cache dir: cache\api_data
2025-06-04 12:49:56,203 - INFO - api_cache_manager - api_cache_manager.py:148 - Cache hit for IN.INTE.KIDS generation (2025-01-01 to 2025-01-03)

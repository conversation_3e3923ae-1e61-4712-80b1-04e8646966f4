2025-06-04 18:22:47,381 - INFO - data - data.py:30 - Smart caching enabled
2025-06-04 18:22:47,381 - INFO - data - data.py:40 - Initializing Prescinto API integration with server: IN
2025-06-04 18:22:47,609 - INFO - data - data.py:42 - API integration initialized successfully
2025-06-04 18:25:08,693 - INFO - data - data.py:30 - Smart caching enabled
2025-06-04 18:25:08,694 - INFO - data - data.py:40 - Initializing Prescinto API integration with server: IN
2025-06-04 18:25:09,007 - INFO - data - data.py:42 - API integration initialized successfully
2025-06-04 18:25:20,946 - WARNING - data - data.py:1468 - Smart cache returned empty data for ANS, falling back to original method
2025-06-04 18:25:20,947 - INFO - data - data.py:1541 - Using plant_id IN.INTE.ANS1 for API calls (display name: ANS)
2025-06-04 18:25:20,948 - INFO - data - data.py:1556 - Fetching data for plant: ANS (ID: IN.INTE.ANS1) from 2025-06-04 to 2025-06-04
2025-06-04 18:25:20,948 - INFO - data - data.py:1560 - Fetching solar plant data with category: Plant, parameter: Daily Energy
2025-06-04 18:25:31,634 - INFO - data - data.py:1571 - Solar data fetch completed for ANS
2025-06-04 18:25:31,634 - INFO - data - data.py:1589 - API returned dataframe with shape: (0, 1) and columns: ['ANS.Daily Energy']
2025-06-04 18:25:31,634 - WARNING - data - data.py:1637 - API returned empty dataframe for ANS
2025-06-04 18:25:31,639 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANS1, date: 2025-06-04
2025-06-04 18:25:31,895 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANS1, trying with Plant Long Name ANS
2025-06-04 18:25:41,778 - WARNING - data - data.py:1468 - Smart cache returned empty data for A N S Paper Mills Pvt Ltd, falling back to original method
2025-06-04 18:25:41,778 - INFO - data - data.py:1541 - Using plant_id IN.INTE.ANSP for API calls (display name: A N S Paper Mills Pvt Ltd)
2025-06-04 18:25:41,778 - INFO - data - data.py:1556 - Fetching data for plant: A N S Paper Mills Pvt Ltd (ID: IN.INTE.ANSP) from 2025-06-04 to 2025-06-04
2025-06-04 18:25:41,778 - INFO - data - data.py:1574 - Fetching wind plant data with category: Turbine, parameter: WTUR.Generation today
2025-06-04 18:25:48,893 - INFO - data - data.py:30 - Smart caching enabled
2025-06-04 18:25:48,893 - INFO - data - data.py:40 - Initializing Prescinto API integration with server: IN
2025-06-04 18:25:49,206 - INFO - data - data.py:42 - API integration initialized successfully
2025-06-04 18:25:51,712 - INFO - data - data.py:1585 - Wind data fetch completed for A N S Paper Mills Pvt Ltd
2025-06-04 18:25:51,712 - INFO - data - data.py:1589 - API returned dataframe with shape: (0, 1) and columns: ['T02.Generation today']
2025-06-04 18:25:51,712 - WARNING - data - data.py:1637 - API returned empty dataframe for A N S Paper Mills Pvt Ltd
2025-06-04 18:25:51,712 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANSP, date: 2025-06-04
2025-06-04 18:25:52,029 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANSP, trying with Plant Long Name A N S Paper Mills Pvt Ltd
2025-06-04 18:26:01,181 - WARNING - data - data.py:1468 - Smart cache returned empty data for ANS, falling back to original method
2025-06-04 18:26:01,181 - INFO - data - data.py:1541 - Using plant_id IN.INTE.ANS1 for API calls (display name: ANS)
2025-06-04 18:26:01,181 - INFO - data - data.py:1556 - Fetching data for plant: ANS (ID: IN.INTE.ANS1) from 2025-06-04 to 2025-06-04
2025-06-04 18:26:01,181 - INFO - data - data.py:1560 - Fetching solar plant data with category: Plant, parameter: Daily Energy
2025-06-04 18:26:11,608 - INFO - data - data.py:1571 - Solar data fetch completed for ANS
2025-06-04 18:26:11,608 - INFO - data - data.py:1589 - API returned dataframe with shape: (0, 1) and columns: ['ANS.Daily Energy']
2025-06-04 18:26:11,608 - WARNING - data - data.py:1637 - API returned empty dataframe for ANS
2025-06-04 18:26:11,608 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANS1, date: 2025-06-04
2025-06-04 18:26:11,847 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANS1, trying with Plant Long Name ANS
2025-06-04 18:26:29,894 - WARNING - data - data.py:1468 - Smart cache returned empty data for A N S Paper Mills Pvt Ltd, falling back to original method
2025-06-04 18:26:29,894 - INFO - data - data.py:1541 - Using plant_id IN.INTE.ANSP for API calls (display name: A N S Paper Mills Pvt Ltd)
2025-06-04 18:26:29,894 - INFO - data - data.py:1556 - Fetching data for plant: A N S Paper Mills Pvt Ltd (ID: IN.INTE.ANSP) from 2025-06-04 to 2025-06-04
2025-06-04 18:26:29,894 - INFO - data - data.py:1574 - Fetching wind plant data with category: Turbine, parameter: WTUR.Generation today
2025-06-04 18:26:47,432 - INFO - data - data.py:1585 - Wind data fetch completed for A N S Paper Mills Pvt Ltd
2025-06-04 18:26:47,432 - INFO - data - data.py:1589 - API returned dataframe with shape: (0, 1) and columns: ['T02.Generation today']
2025-06-04 18:26:47,432 - WARNING - data - data.py:1637 - API returned empty dataframe for A N S Paper Mills Pvt Ltd
2025-06-04 18:26:47,439 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANSP, date: 2025-06-04
2025-06-04 18:26:47,730 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANSP, trying with Plant Long Name A N S Paper Mills Pvt Ltd
2025-06-04 18:27:02,026 - INFO - data - data.py:30 - Smart caching enabled
2025-06-04 18:27:02,026 - INFO - data - data.py:40 - Initializing Prescinto API integration with server: IN
2025-06-04 18:27:02,267 - INFO - data - data.py:42 - API integration initialized successfully
2025-06-04 18:27:29,764 - INFO - data - data.py:1465 - Smart cache returned 96 rows for ANS
2025-06-04 18:27:29,770 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANS1, date: 2025-01-01
2025-06-04 18:27:30,022 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANS1, trying with Plant Long Name ANS
2025-06-04 18:27:44,416 - INFO - data - data.py:1465 - Smart cache returned 96 rows for ANS
2025-06-04 18:27:44,418 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANS1, date: 2025-04-01
2025-06-04 18:27:44,650 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANS1, trying with Plant Long Name ANS
2025-06-04 18:27:45,679 - INFO - data - data.py:1465 - Smart cache returned 9 rows for ANS
2025-06-04 18:27:45,682 - INFO - data - data.py:592 - Loading hourly consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANS1, date range: 2025-04-01 to 2025-04-09
2025-06-04 18:27:45,923 - WARNING - data - data.py:624 - No data found for plant_id IN.INTE.ANS1, trying with Plant Long Name ANS
2025-06-04 18:27:58,585 - INFO - data - data.py:1465 - Smart cache returned 96 rows for A N S Paper Mills Pvt Ltd
2025-06-04 18:27:58,590 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANSP, date: 2025-01-01
2025-06-04 18:27:58,841 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANSP, trying with Plant Long Name A N S Paper Mills Pvt Ltd
2025-06-04 18:28:08,170 - INFO - data - data.py:1465 - Smart cache returned 96 rows for A N S Paper Mills Pvt Ltd
2025-06-04 18:28:08,173 - INFO - data - data.py:324 - Loading consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANSP, date: 2025-04-01
2025-06-04 18:28:08,421 - WARNING - data - data.py:354 - No data found for plant_id IN.INTE.ANSP, trying with Plant Long Name A N S Paper Mills Pvt Ltd
2025-06-04 18:28:09,191 - INFO - data - data.py:1465 - Smart cache returned 9 rows for A N S Paper Mills Pvt Ltd
2025-06-04 18:28:09,195 - INFO - data - data.py:592 - Loading hourly consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: IN.INTE.ANSP, date range: 2025-04-01 to 2025-04-09
2025-06-04 18:28:09,493 - WARNING - data - data.py:624 - No data found for plant_id IN.INTE.ANSP, trying with Plant Long Name A N S Paper Mills Pvt Ltd
2025-06-04 18:28:10,063 - WARNING - data - data.py:1468 - Smart cache returned empty data for Combined View, falling back to original method
2025-06-04 18:28:10,063 - INFO - data - data.py:1534 - Combined View is not a real plant, handling separately
2025-06-04 18:28:10,063 - WARNING - data - data.py:1818 - No generation data found for Combined View between 2025-04-01 and 2025-04-09
2025-06-04 18:28:10,072 - INFO - data - data.py:2061 - Solar plants for ANS Paper Mills Pvt Ltd: ['IN.INTE.ANS1']
2025-06-04 18:28:10,072 - INFO - data - data.py:2066 - Wind plants for ANS Paper Mills Pvt Ltd: ['IN.INTE.ANSP']
2025-06-04 18:28:10,072 - INFO - data - data.py:2081 - Using daily granularity for date range: 2025-04-01 to 2025-04-09
2025-06-04 18:28:10,072 - INFO - data - data.py:2100 - Fetching data for solar plant: IN.INTE.ANS1 from 2025-04-01 to 2025-04-09 with 1d granularity
2025-06-04 18:28:25,896 - INFO - data - data.py:2115 - API returned solar plant dataframe with shape: (9, 2)
2025-06-04 18:28:25,907 - INFO - data - data.py:2155 - Added 9 rows of solar generation data for IN.INTE.ANS1
2025-06-04 18:28:25,907 - INFO - data - data.py:2175 - Fetching data for wind plant: IN.INTE.ANSP from 2025-04-01 to 2025-04-09 with 1d granularity
2025-06-04 18:28:40,036 - INFO - data - data.py:2189 - API returned wind plant dataframe with shape: (9, 2)
2025-06-04 18:28:40,040 - INFO - data - data.py:2230 - Added 9 rows of wind generation data for IN.INTE.ANSP
2025-06-04 18:28:40,041 - INFO - data - data.py:2258 - Plant: IN.INTE.ANS1, Source: Solar
2025-06-04 18:28:40,042 - INFO - data - data.py:2258 - Plant: IN.INTE.ANSP, Source: Wind
2025-06-04 18:28:40,042 - INFO - data - data.py:2261 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-06-04 18:28:40,042 - INFO - data - data.py:2263 - Retrieved combined wind and solar generation data for ANS Paper Mills Pvt Ltd: 18 rows
2025-06-04 18:28:40,741 - INFO - data - data.py:592 - Loading hourly consumption data from: Data/csv/Consumption data Cloud nine - processed_data.csv for plant_id: Combined View, date range: 2025-04-01 to 2025-04-09
2025-06-04 18:28:40,995 - WARNING - data - data.py:624 - No data found for plant_id Combined View, trying with Plant Long Name Combined View

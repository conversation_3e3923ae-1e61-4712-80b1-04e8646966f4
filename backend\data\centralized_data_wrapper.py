"""
Wrapper functions for centralized data management.

This module provides wrapper functions that replace the existing data fetching functions
in display_components.py, redirecting them to use the centralized data manager.
"""

import pandas as pd
from datetime import datetime
from typing import Tuple, Optional

from backend.logs.logger_setup import setup_logger
from backend.data.centralized_data_manager import centralized_data_manager

logger = setup_logger('centralized_data_wrapper', 'centralized_data_wrapper.log')

def ensure_data_loaded(client_name: str, selected_plant: str, 
                      start_date: datetime, end_date: datetime) -> bool:
    """
    Ensure that data is loaded in the centralized data manager.
    
    Args:
        client_name: Name of the selected client
        selected_plant: Name of the selected plant
        start_date: Start date for data
        end_date: End date for data
        
    Returns:
        bool: True if data is available, False otherwise
    """
    try:
        # Check if data needs to be fetched
        if centralized_data_manager.should_refresh_data(client_name, selected_plant, start_date, end_date):
            logger.info(f"Fetching data for {client_name}/{selected_plant} from {start_date} to {end_date}")
            success = centralized_data_manager.fetch_all_data(client_name, selected_plant, start_date, end_date)
            if not success:
                logger.error("Failed to fetch data")
                return False
        
        return centralized_data_manager.is_data_available(selected_plant if selected_plant != "Combined View" else None)
        
    except Exception as e:
        logger.error(f"Error ensuring data is loaded: {e}")
        return False

# Wrapper functions that replace existing data fetching functions

def get_consumption_data_from_csv(plant_name: str, date: datetime) -> pd.DataFrame:
    """
    Wrapper for get_consumption_data_from_csv that uses centralized data manager.
    """
    try:
        # For single date, we need to determine the client
        # This is a limitation - we need client context
        # For now, we'll try to get data if it's already loaded
        df = centralized_data_manager.get_consumption_data(plant_name)
        
        if df.empty:
            logger.warning(f"No consumption data available for {plant_name} on {date}")
            # Fallback to original function if needed
            from backend.data.data import get_consumption_data_from_csv as original_func
            return original_func(plant_name, date)
        
        # Filter for the specific date if we have date column
        if 'date' in df.columns:
            df['date'] = pd.to_datetime(df['date'])
            df = df[df['date'].dt.date == date.date()]
        
        return df
        
    except Exception as e:
        logger.error(f"Error in get_consumption_data_from_csv wrapper: {e}")
        return pd.DataFrame()

def get_generation_consumption_comparison(plant_name: str, date: datetime) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Wrapper for get_generation_consumption_comparison that uses centralized data manager.
    """
    try:
        # Get generation data
        gen_df = centralized_data_manager.get_generation_data(plant_name)
        
        # Get consumption data
        cons_df = centralized_data_manager.get_consumption_data(plant_name)
        
        # Filter for the specific date if we have date column
        if not gen_df.empty and 'date' in gen_df.columns:
            gen_df['date'] = pd.to_datetime(gen_df['date'])
            gen_df = gen_df[gen_df['date'].dt.date == date.date()]
        
        if not cons_df.empty and 'date' in cons_df.columns:
            cons_df['date'] = pd.to_datetime(cons_df['date'])
            cons_df = cons_df[cons_df['date'].dt.date == date.date()]
        
        return gen_df, cons_df
        
    except Exception as e:
        logger.error(f"Error in get_generation_consumption_comparison wrapper: {e}")
        return pd.DataFrame(), pd.DataFrame()

def compare_generation_consumption(generation_df: pd.DataFrame, consumption_df: pd.DataFrame) -> pd.DataFrame:
    """
    Wrapper for compare_generation_consumption - this function doesn't need modification
    as it just processes already fetched data.
    """
    try:
        from backend.data.data import compare_generation_consumption as original_func
        return original_func(generation_df, consumption_df)
    except Exception as e:
        logger.error(f"Error in compare_generation_consumption wrapper: {e}")
        return pd.DataFrame()

def get_daily_consumption_data(plant_name: str, start_date: datetime, end_date: datetime) -> pd.DataFrame:
    """
    Wrapper for get_daily_consumption_data that uses centralized data manager.
    """
    try:
        df = centralized_data_manager.get_consumption_data(plant_name)
        
        if df.empty:
            logger.warning(f"No consumption data available for {plant_name}")
            # Fallback to original function if needed
            from backend.data.data import get_daily_consumption_data as original_func
            return original_func(plant_name, start_date, end_date)
        
        # Filter for the date range if we have date column
        if 'date' in df.columns:
            df['date'] = pd.to_datetime(df['date'])
            df = df[(df['date'].dt.date >= start_date.date()) & 
                   (df['date'].dt.date <= end_date.date())]
        
        return df
        
    except Exception as e:
        logger.error(f"Error in get_daily_consumption_data wrapper: {e}")
        return pd.DataFrame()

def get_daily_generation_consumption_comparison(plant_name: str, start_date: datetime, end_date: datetime) -> pd.DataFrame:
    """
    Wrapper for get_daily_generation_consumption_comparison that uses centralized data manager.
    """
    try:
        df = centralized_data_manager.get_combined_data(plant_name)
        
        if df.empty:
            logger.warning(f"No combined data available for {plant_name}")
            # Fallback to original function if needed
            from backend.data.data import get_daily_generation_consumption_comparison as original_func
            return original_func(plant_name, start_date, end_date)
        
        # Filter for the date range if we have date column
        if 'date' in df.columns:
            df['date'] = pd.to_datetime(df['date'])
            df = df[(df['date'].dt.date >= start_date.date()) & 
                   (df['date'].dt.date <= end_date.date())]
        
        return df
        
    except Exception as e:
        logger.error(f"Error in get_daily_generation_consumption_comparison wrapper: {e}")
        return pd.DataFrame()

def get_combined_wind_solar_generation(client_name: str, start_date: datetime, end_date: datetime) -> pd.DataFrame:
    """
    Wrapper for get_combined_wind_solar_generation that uses centralized data manager.
    """
    try:
        df = centralized_data_manager.get_combined_wind_solar_data(client_name)
        
        if df.empty:
            logger.warning(f"No combined wind/solar data available for {client_name}")
            # Fallback to original function if needed
            from backend.data.data import get_combined_wind_solar_generation as original_func
            return original_func(client_name, start_date, end_date)
        
        # Filter for the date range if we have date column
        if 'date' in df.columns:
            df['date'] = pd.to_datetime(df['date'])
            df = df[(df['date'].dt.date >= start_date.date()) & 
                   (df['date'].dt.date <= end_date.date())]
        
        return df
        
    except Exception as e:
        logger.error(f"Error in get_combined_wind_solar_generation wrapper: {e}")
        return pd.DataFrame()

def get_tod_binned_data(plant_name: str, start_date: datetime, end_date: Optional[datetime] = None) -> pd.DataFrame:
    """
    Wrapper for get_tod_binned_data that uses centralized data manager.
    """
    try:
        df = centralized_data_manager.get_tod_data(plant_name)
        
        if df.empty:
            logger.warning(f"No ToD data available for {plant_name}")
            # Fallback to original function if needed
            from backend.data.data import get_tod_binned_data as original_func
            return original_func(plant_name, start_date, end_date)
        
        return df
        
    except Exception as e:
        logger.error(f"Error in get_tod_binned_data wrapper: {e}")
        return pd.DataFrame()

def get_banking_data(plant_name: str, start_date: datetime, end_date: Optional[datetime] = None, 
                    banking_type: str = "daily", tod_based: bool = False) -> pd.DataFrame:
    """
    Wrapper for get_banking_data that uses centralized data manager.
    """
    try:
        df = centralized_data_manager.get_banking_data(plant_name, banking_type, tod_based)
        
        if df.empty:
            logger.warning(f"No banking data available for {plant_name}")
            # Fallback to original function if needed
            from backend.data.data import get_banking_data as original_func
            return original_func(plant_name, start_date, end_date, banking_type, tod_based)
        
        return df
        
    except Exception as e:
        logger.error(f"Error in get_banking_data wrapper: {e}")
        return pd.DataFrame()

def get_generation_only_data(plant_name: str, date: datetime) -> pd.DataFrame:
    """
    Wrapper for get_generation_only_data that uses centralized data manager.
    """
    try:
        df = centralized_data_manager.get_generation_data(plant_name)
        
        if df.empty:
            logger.warning(f"No generation data available for {plant_name}")
            # Fallback to original function if needed
            from backend.data.data import get_generation_only_data as original_func
            return original_func(plant_name, date)
        
        # Filter for the specific date if we have date column
        if 'date' in df.columns:
            df['date'] = pd.to_datetime(df['date'])
            df = df[df['date'].dt.date == date.date()]
        
        return df
        
    except Exception as e:
        logger.error(f"Error in get_generation_only_data wrapper: {e}")
        return pd.DataFrame()

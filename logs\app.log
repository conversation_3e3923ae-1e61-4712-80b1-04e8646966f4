2025-05-27 12:35:06,272 - ERROR - app - app.py:170 - Application error: cannot access local variable 'all_days_df' where it is not associated with a value
2025-05-27 12:35:06,272 - ERROR - app - app.py:171 - Traceback (most recent call last):
  File "D:\<PERSON><PERSON><PERSON><PERSON><PERSON>\Plot Generation\app.py", line 148, in main
    display_tod_generation_view(selected_plant, start_date, end_date, section="tod")
  File "D:\<PERSON><PERSON><PERSON><PERSON>an\Plot Generation\src\display_components.py", line 1458, in display_tod_generation_view
    df=df if is_single_day else all_days_df,
                                ^^^^^^^^^^^
UnboundLocalError: cannot access local variable 'all_days_df' where it is not associated with a value

2025-06-04 15:51:01,486 - INFO - app - app.py:62 - Async integration initialized successfully
2025-06-04 15:51:01,556 - INFO - app - app.py:68 - Async visualization manager ready
2025-06-04 15:51:01,567 - INFO - app - app.py:95 - Threading system initialized successfully
2025-06-04 15:51:52,985 - INFO - app - app.py:62 - Async integration initialized successfully
2025-06-04 15:51:52,985 - INFO - app - app.py:68 - Async visualization manager ready
2025-06-04 15:51:52,985 - INFO - app - app.py:95 - Threading system initialized successfully
2025-06-04 15:52:13,839 - INFO - app - app.py:62 - Async integration initialized successfully
2025-06-04 15:52:13,839 - INFO - app - app.py:68 - Async visualization manager ready
2025-06-04 15:52:13,847 - INFO - app - app.py:95 - Threading system initialized successfully
2025-06-04 16:00:12,407 - INFO - app - app.py:62 - Async integration initialized successfully
2025-06-04 16:00:12,407 - INFO - app - app.py:68 - Async visualization manager ready
2025-06-04 16:00:12,407 - INFO - app - app.py:95 - Threading system initialized successfully
2025-06-04 16:00:12,426 - INFO - app - app.py:159 - Auto-triggering threads for new state: Combined View_ANS Paper Mills Pvt Ltd_2025-06-04_2025-06-04_True_True
2025-06-04 16:00:12,488 - INFO - app - app.py:175 - All background threads started automatically

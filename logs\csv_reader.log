2025-05-27 15:53:29,976 - INFO - csv_reader - csv_reader.py:37 - CSV reader initialized with path: data/csv/Consumption data Cloud nine - processed_data.csv
2025-05-27 15:53:29,982 - ERROR - csv_reader - csv_reader.py:59 - Consumption CSV file not found: data/csv/Consumption data Cloud nine - processed_data.csv
2025-05-27 15:53:29,984 - WARNING - csv_reader - csv_reader.py:170 - No consumption data available
2025-05-27 15:55:04,828 - ERROR - csv_reader - csv_reader.py:59 - Consumption CSV file not found: data/csv/Consumption data Cloud nine - processed_data.csv
2025-05-27 15:55:04,830 - WARNING - csv_reader - csv_reader.py:170 - No consumption data available
2025-05-27 15:55:10,416 - ERROR - csv_reader - csv_reader.py:59 - Consumption CSV file not found: data/csv/Consumption data Cloud nine - processed_data.csv
2025-05-27 15:55:10,419 - WARNING - csv_reader - csv_reader.py:170 - No consumption data available
2025-05-27 15:55:11,131 - ERROR - csv_reader - csv_reader.py:59 - Consumption CSV file not found: data/csv/Consumption data Cloud nine - processed_data.csv
2025-05-27 15:55:11,135 - WARNING - csv_reader - csv_reader.py:170 - No consumption data available
2025-05-27 15:55:11,142 - ERROR - csv_reader - csv_reader.py:59 - Consumption CSV file not found: data/csv/Consumption data Cloud nine - processed_data.csv
2025-05-27 15:55:11,145 - WARNING - csv_reader - csv_reader.py:170 - No consumption data available
2025-05-27 15:55:30,003 - INFO - csv_reader - csv_reader.py:37 - CSV reader initialized with path: data/csv/Consumption data Cloud nine - processed_data.csv
2025-05-27 15:55:30,004 - ERROR - csv_reader - csv_reader.py:59 - Consumption CSV file not found: data/csv/Consumption data Cloud nine - processed_data.csv
2025-05-27 15:55:30,006 - WARNING - csv_reader - csv_reader.py:170 - No consumption data available
2025-05-27 16:17:52,903 - INFO - csv_reader - csv_reader.py:37 - CSV reader initialized with path: data/csv/Consumption data Cloud nine - processed_data.csv
2025-05-27 16:17:52,903 - ERROR - csv_reader - csv_reader.py:59 - Consumption CSV file not found: data/csv/Consumption data Cloud nine - processed_data.csv
2025-05-27 16:17:52,905 - WARNING - csv_reader - csv_reader.py:170 - No consumption data available
2025-05-27 16:18:02,843 - ERROR - csv_reader - csv_reader.py:59 - Consumption CSV file not found: data/csv/Consumption data Cloud nine - processed_data.csv
2025-05-27 16:18:02,845 - WARNING - csv_reader - csv_reader.py:170 - No consumption data available
2025-05-27 16:18:13,919 - INFO - csv_reader - csv_reader.py:37 - CSV reader initialized with path: data/csv/Consumption data Cloud nine - processed_data.csv
2025-05-27 16:18:13,920 - ERROR - csv_reader - csv_reader.py:59 - Consumption CSV file not found: data/csv/Consumption data Cloud nine - processed_data.csv
2025-05-27 16:18:13,921 - WARNING - csv_reader - csv_reader.py:170 - No consumption data available
2025-05-27 16:18:20,165 - ERROR - csv_reader - csv_reader.py:59 - Consumption CSV file not found: data/csv/Consumption data Cloud nine - processed_data.csv
2025-05-27 16:18:20,168 - WARNING - csv_reader - csv_reader.py:170 - No consumption data available
2025-05-27 16:19:20,067 - INFO - csv_reader - csv_reader.py:37 - Using test consumption data file
2025-05-27 16:19:20,067 - INFO - csv_reader - csv_reader.py:45 - CSV reader initialized with path: data/csv/test_consumption.csv
2025-05-27 16:19:20,067 - INFO - csv_reader - csv_reader.py:70 - Loading consumption CSV from: data/csv/test_consumption.csv
2025-05-27 16:19:20,091 - INFO - csv_reader - csv_reader.py:87 - Loaded 16 rows from consumption CSV
2025-05-27 16:19:20,091 - INFO - csv_reader - csv_reader.py:234 - Found 2 plants in consumption data
2025-05-27 16:19:20,095 - INFO - csv_reader - csv_reader.py:210 - Retrieved 8 rows of consumption data for ANS
2025-05-27 16:19:20,097 - INFO - csv_reader - csv_reader.py:210 - Retrieved 8 rows of consumption data for Kids Clinic India Limited
2025-05-27 16:21:15,220 - INFO - csv_reader - csv_reader.py:37 - Using test consumption data file
2025-05-27 16:21:15,220 - INFO - csv_reader - csv_reader.py:45 - CSV reader initialized with path: data/csv/test_consumption.csv
2025-05-27 16:21:15,221 - INFO - csv_reader - csv_reader.py:70 - Loading consumption CSV from: data/csv/test_consumption.csv
2025-05-27 16:21:15,232 - INFO - csv_reader - csv_reader.py:87 - Loaded 16 rows from consumption CSV
2025-05-27 16:21:15,232 - ERROR - csv_reader - csv_reader.py:214 - Error getting consumption data for Kids Clinic India Limited: Invalid comparison between dtype=datetime64[ns] and date
2025-05-27 16:23:30,453 - INFO - csv_reader - csv_reader.py:37 - Using test consumption data file
2025-05-27 16:23:30,453 - INFO - csv_reader - csv_reader.py:45 - CSV reader initialized with path: data/csv/test_consumption.csv
2025-05-27 16:23:30,454 - INFO - csv_reader - csv_reader.py:70 - Loading consumption CSV from: data/csv/test_consumption.csv
2025-05-27 16:23:30,466 - INFO - csv_reader - csv_reader.py:87 - Loaded 16 rows from consumption CSV
2025-05-27 16:23:30,469 - WARNING - csv_reader - csv_reader.py:191 - No consumption data found for plant: Combined View
2025-05-27 16:29:09,590 - WARNING - csv_reader - csv_reader.py:191 - No consumption data found for plant: Combined View
2025-05-27 16:29:18,778 - WARNING - csv_reader - csv_reader.py:191 - No consumption data found for plant: Combined View
2025-05-27 16:29:18,979 - WARNING - csv_reader - csv_reader.py:191 - No consumption data found for plant: Combined View
2025-05-27 16:44:38,269 - INFO - csv_reader - csv_reader.py:37 - Using test consumption data file
2025-05-27 16:44:38,269 - INFO - csv_reader - csv_reader.py:45 - CSV reader initialized with path: data/csv/test_consumption.csv
2025-05-27 16:44:38,270 - INFO - csv_reader - csv_reader.py:70 - Loading consumption CSV from: data/csv/test_consumption.csv
2025-05-27 16:44:38,277 - INFO - csv_reader - csv_reader.py:87 - Loaded 16 rows from consumption CSV
2025-05-27 16:44:38,279 - WARNING - csv_reader - csv_reader.py:191 - No consumption data found for plant: Combined View
2025-05-27 16:48:10,323 - WARNING - csv_reader - csv_reader.py:191 - No consumption data found for plant: Combined View
2025-05-27 16:48:38,124 - INFO - csv_reader - csv_reader.py:37 - Using test consumption data file
2025-05-27 16:48:38,124 - INFO - csv_reader - csv_reader.py:45 - CSV reader initialized with path: data/csv/test_consumption.csv
2025-05-27 16:48:38,124 - INFO - csv_reader - csv_reader.py:70 - Loading consumption CSV from: data/csv/test_consumption.csv
2025-05-27 16:48:38,135 - INFO - csv_reader - csv_reader.py:87 - Loaded 16 rows from consumption CSV
2025-05-27 16:48:38,138 - WARNING - csv_reader - csv_reader.py:191 - No consumption data found for plant: Combined View

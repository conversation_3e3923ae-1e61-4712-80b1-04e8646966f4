"""
🎯 DEMONSTRATION: Single API Call Strategy

This script demonstrates the new single API call strategy implementation.

Expected Behavior:
1. ✅ One API call per plant → store result → parse once
2. 💾 All data cached in memory for instant access
3. 📊 All plots rendered from cached data only
4. ⛔ No additional API calls during visualization
5. 🚫 No repeated JSON parsing warnings

Usage:
    python demo_single_api_call.py
"""

import time
from datetime import datetime
import pandas as pd

from backend.data.centralized_data_manager import centralized_data_manager
from backend.data.centralized_data_wrapper import ensure_data_loaded
from backend.logs.logger_setup import setup_logger

# Setup logging
logger = setup_logger('demo', 'demo.log')

def demonstrate_single_api_call():
    """Demonstrate the single API call strategy."""
    
    print("🎯 SINGLE API CALL STRATEGY DEMONSTRATION")
    print("=" * 50)
    
    # Test parameters
    client_name = "KIDS Clinic"
    plant_name = "KIDS Clinic"
    test_date = datetime(2025, 6, 3)
    
    print(f"📋 Test Parameters:")
    print(f"   Client: {client_name}")
    print(f"   Plant: {plant_name}")
    print(f"   Date: {test_date.strftime('%Y-%m-%d')}")
    print()
    
    # Clear any existing cache
    print("🗑️ Clearing existing cache...")
    centralized_data_manager.clear_cache()
    print("✅ Cache cleared")
    print()
    
    # Demonstrate single API call
    print("🚀 STEP 1: Single API Call Strategy")
    print("-" * 30)
    
    start_time = time.time()
    
    print("📡 Making single API call to fetch ALL data...")
    success = ensure_data_loaded(client_name, plant_name, test_date, test_date)
    
    fetch_duration = time.time() - start_time
    
    if success:
        print(f"✅ Data fetched successfully in {fetch_duration:.2f} seconds")
        
        # Show what data is now cached
        data_summary = centralized_data_manager.get_data_summary()
        print(f"💾 Cached data summary:")
        print(f"   Generation plants: {len(data_summary['generation_plants'])}")
        print(f"   Consumption plants: {len(data_summary['consumption_plants'])}")
        print(f"   Combined data plants: {len(data_summary['combined_plants'])}")
        print(f"   ToD data plants: {len(data_summary['tod_plants'])}")
        print(f"   Fetch timestamp: {data_summary['fetch_timestamp']}")
    else:
        print("❌ Data fetch failed")
        return
    
    print()
    
    # Demonstrate visualization using cached data
    print("📊 STEP 2: Visualization Using Cached Data Only")
    print("-" * 45)
    
    print("🚫 NO additional API calls will be made for the following operations:")
    print()
    
    # Simulate multiple plot generations
    plots = [
        ("Generation Data", lambda: centralized_data_manager.get_generation_data(plant_name)),
        ("Consumption Data", lambda: centralized_data_manager.get_consumption_data(plant_name)),
        ("Combined Gen vs Cons", lambda: centralized_data_manager.get_combined_data(plant_name)),
        ("ToD Analysis", lambda: centralized_data_manager.get_tod_data(plant_name)),
        ("Wind/Solar Combined", lambda: centralized_data_manager.get_combined_wind_solar_data(client_name))
    ]
    
    total_viz_time = 0
    
    for plot_name, plot_func in plots:
        viz_start = time.time()
        
        print(f"   🔄 Rendering {plot_name}...", end=" ")
        
        try:
            data = plot_func()
            viz_duration = time.time() - viz_start
            total_viz_time += viz_duration
            
            if not data.empty:
                print(f"✅ {len(data)} rows ({viz_duration:.3f}s)")
            else:
                print(f"⚠️ No data ({viz_duration:.3f}s)")
                
        except Exception as e:
            viz_duration = time.time() - viz_start
            total_viz_time += viz_duration
            print(f"❌ Error: {e} ({viz_duration:.3f}s)")
    
    print()
    print(f"📊 Total visualization time: {total_viz_time:.3f}s")
    print("✅ All visualizations completed using cached data only!")
    print()
    
    # Demonstrate cache efficiency
    print("⚡ STEP 3: Cache Efficiency Test")
    print("-" * 30)
    
    print("🔄 Accessing same data 10 times to show cache efficiency...")
    
    cache_start = time.time()
    for i in range(10):
        gen_data = centralized_data_manager.get_generation_data(plant_name)
        cons_data = centralized_data_manager.get_consumption_data(plant_name)
        combined_data = centralized_data_manager.get_combined_data(plant_name)
    
    cache_duration = time.time() - cache_start
    
    print(f"✅ 30 data access operations completed in {cache_duration:.3f}s")
    print(f"⚡ Average access time: {(cache_duration/30)*1000:.1f}ms per operation")
    print()
    
    # Demonstrate cache persistence
    print("💾 STEP 4: Cache Persistence Test")
    print("-" * 30)
    
    print("🔄 Testing if second call uses cache (no API call)...")
    
    second_call_start = time.time()
    success2 = ensure_data_loaded(client_name, plant_name, test_date, test_date)
    second_call_duration = time.time() - second_call_start
    
    if success2:
        print(f"✅ Second call completed in {second_call_duration:.3f}s (using cache)")
        if second_call_duration < 0.1:
            print("🚀 Cache hit confirmed - no API call made!")
        else:
            print("⚠️ Possible API call - cache may not be working optimally")
    else:
        print("❌ Second call failed")
    
    print()
    
    # Performance summary
    print("📈 PERFORMANCE SUMMARY")
    print("=" * 25)
    print(f"🚀 Initial data fetch: {fetch_duration:.2f}s")
    print(f"📊 All visualizations: {total_viz_time:.3f}s")
    print(f"💾 Cache access (30 ops): {cache_duration:.3f}s")
    print(f"⚡ Second data load: {second_call_duration:.3f}s")
    print()
    
    # Calculate performance improvement
    estimated_old_time = fetch_duration * 5  # Assume 5 API calls in old system
    actual_new_time = fetch_duration + total_viz_time
    improvement = ((estimated_old_time - actual_new_time) / estimated_old_time) * 100
    
    print(f"🎯 ESTIMATED PERFORMANCE IMPROVEMENT:")
    print(f"   Old system (multiple API calls): ~{estimated_old_time:.2f}s")
    print(f"   New system (single API call): {actual_new_time:.2f}s")
    print(f"   Performance improvement: {improvement:.1f}%")
    print()
    
    print("✅ DEMONSTRATION COMPLETED SUCCESSFULLY!")
    print("🎉 Single API call strategy is working as expected!")

def demonstrate_error_handling():
    """Demonstrate error handling in the single API call strategy."""
    
    print("\n🛡️ ERROR HANDLING DEMONSTRATION")
    print("=" * 35)
    
    # Test with invalid plant
    print("❌ Testing with invalid plant name...")
    
    start_time = time.time()
    success = ensure_data_loaded("Invalid Client", "Invalid Plant", 
                               datetime(2025, 6, 3), datetime(2025, 6, 3))
    duration = time.time() - start_time
    
    if not success:
        print(f"✅ Error handled gracefully in {duration:.2f}s")
        print("🔍 Check logs for detailed error information")
    else:
        print("⚠️ Unexpected success with invalid parameters")
    
    print()

if __name__ == "__main__":
    try:
        demonstrate_single_api_call()
        demonstrate_error_handling()
        
    except Exception as e:
        print(f"❌ Demonstration failed: {e}")
        logger.error(f"Demonstration error: {e}")
    
    print("\n📋 Check the following log files for detailed information:")
    print("   • logs/demo.log - Demonstration logs")
    print("   • logs/centralized_data_manager.log - Data manager logs")
    print("   • logs/data.log - API call logs")

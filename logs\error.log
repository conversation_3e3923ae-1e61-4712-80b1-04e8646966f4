2025-05-28 16:08:16 - ERROR - error_only - error_logger.py:66 - [integration] Response status code: 401
2025-05-28 16:08:16 - ERROR - error_only - error_logger.py:66 - [integration] Response error: {"status":false,"result":"You are not authorized to access Combined View plant."}
2025-05-29 17:40:37 - ERROR - error_only - error_logger.py:66 - [integration] Response status code: 401
2025-05-29 17:40:37 - ERROR - error_only - error_logger.py:66 - [integration] Response error: {"status":false,"result":"You are not authorized to access Combined View plant."}
2025-05-29 17:40:38 - ERROR - error_only - error_logger.py:66 - [integration] Response status code: 401
2025-05-29 17:40:38 - ERROR - error_only - error_logger.py:66 - [integration] Response error: {"status":false,"result":"You are not authorized to access Combined View plant."}
2025-05-29 17:40:39 - ERROR - error_only - error_logger.py:66 - [integration] Response status code: 401
2025-05-29 17:40:39 - ERROR - error_only - error_logger.py:66 - [integration] Response error: {"status":false,"result":"You are not authorized to access Combined View plant."}
2025-05-29 17:50:32 - ERROR - error_only - error_logger.py:66 - [integration] Response status code: 401
2025-05-29 17:50:32 - ERROR - error_only - error_logger.py:66 - [integration] Response status code: 401
2025-05-29 17:50:32 - ERROR - error_only - error_logger.py:66 - [integration] Response status code: 401
2025-05-29 17:50:32 - ERROR - error_only - error_logger.py:66 - [integration] Response error: {"status":false,"result":"You are not authorized to access Combined View plant."}
2025-05-29 17:50:32 - ERROR - error_only - error_logger.py:66 - [integration] Response error: {"status":false,"result":"You are not authorized to access Combined View plant."}
2025-05-29 17:50:32 - ERROR - error_only - error_logger.py:66 - [integration] Response error: {"status":false,"result":"You are not authorized to access Combined View plant."}
2025-06-03 15:59:58 - ERROR - error_only - error_logger.py:66 - [data] API call failed for solar plant IN.INTE.ANS1: 'str' object has no attribute 'empty'
2025-06-03 15:59:58 - ERROR - error_only - error_logger.py:66 - [data] Traceback (most recent call last):
  File "D:\Harikrishnan\Plot Generation\backend\data\data.py", line 2062, in get_combined_wind_solar_generation
    if not plant_df.empty:
           ^^^^^^^^^^^^^^
AttributeError: 'str' object has no attribute 'empty'

2025-06-03 16:00:09 - ERROR - error_only - error_logger.py:66 - [data] Error fetching generation data for {'name': 'Kids Clinic India Limited', 'plant_id': 'IN.INTE.KIDS'}: 'str' object has no attribute 'empty'
2025-06-03 16:00:09 - ERROR - error_only - error_logger.py:66 - [data] Traceback (most recent call last):
  File "D:\Harikrishnan\Plot Generation\backend\data\data.py", line 883, in get_generation_only_data
    if df is None or df.empty:
                     ^^^^^^^^
AttributeError: 'str' object has no attribute 'empty'

2025-06-03 16:00:11 - ERROR - error_only - error_logger.py:66 - [data] Error fetching generation data for {'name': 'Kids Clinic India Limited', 'plant_id': 'IN.INTE.KIDS'}: 'str' object has no attribute 'empty'
2025-06-03 16:00:11 - ERROR - error_only - error_logger.py:66 - [data] Traceback (most recent call last):
  File "D:\Harikrishnan\Plot Generation\backend\data\data.py", line 883, in get_generation_only_data
    if df is None or df.empty:
                     ^^^^^^^^
AttributeError: 'str' object has no attribute 'empty'

2025-06-03 16:00:19 - ERROR - error_only - error_logger.py:66 - [data] Error fetching generation data for {'name': 'Kids Clinic India Limited', 'plant_id': 'IN.INTE.KIDS'}: 'str' object has no attribute 'empty'
2025-06-03 16:00:19 - ERROR - error_only - error_logger.py:66 - [data] Traceback (most recent call last):
  File "D:\Harikrishnan\Plot Generation\backend\data\data.py", line 883, in get_generation_only_data
    if df is None or df.empty:
                     ^^^^^^^^
AttributeError: 'str' object has no attribute 'empty'

2025-06-03 16:00:42 - ERROR - error_only - error_logger.py:66 - [data] API call failed for wind plant IN.INTE.ANSP: 'str' object has no attribute 'empty'
2025-06-03 16:00:42 - ERROR - error_only - error_logger.py:66 - [data] Traceback (most recent call last):
  File "D:\Harikrishnan\Plot Generation\backend\data\data.py", line 2138, in get_combined_wind_solar_generation
    if not plant_df.empty:
           ^^^^^^^^^^^^^^
AttributeError: 'str' object has no attribute 'empty'

2025-06-03 16:00:42 - ERROR - error_only - error_logger.py:66 - [data] Error getting wind generation data for IN.INTE.ANSP: 'str' object does not support item assignment
2025-06-03 16:00:42 - ERROR - error_only - error_logger.py:66 - [data] Traceback (most recent call last):
  File "D:\Harikrishnan\Plot Generation\backend\data\data.py", line 2138, in get_combined_wind_solar_generation
    if not plant_df.empty:
           ^^^^^^^^^^^^^^
AttributeError: 'str' object has no attribute 'empty'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Harikrishnan\Plot Generation\backend\data\data.py", line 2176, in get_combined_wind_solar_generation
    plant_df['PLANT_LONG_NAME'] = plant_name
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^
TypeError: 'str' object does not support item assignment

2025-06-03 16:00:58 - ERROR - error_only - error_logger.py:66 - [data] Failed to retrieve generation data from API: 'str' object has no attribute 'empty'
2025-06-03 16:00:58 - ERROR - error_only - error_logger.py:66 - [data] Traceback (most recent call last):
  File "D:\Harikrishnan\Plot Generation\backend\data\data.py", line 1542, in get_generation_data
    if not df.empty:
           ^^^^^^^^
AttributeError: 'str' object has no attribute 'empty'

2025-06-03 16:01:17 - ERROR - error_only - error_logger.py:66 - [data] Failed to retrieve generation data from API: 'str' object has no attribute 'empty'
2025-06-03 16:01:17 - ERROR - error_only - error_logger.py:66 - [data] Traceback (most recent call last):
  File "D:\Harikrishnan\Plot Generation\backend\data\data.py", line 1542, in get_generation_data
    if not df.empty:
           ^^^^^^^^
AttributeError: 'str' object has no attribute 'empty'

2025-06-03 16:01:23 - ERROR - error_only - error_logger.py:66 - [data] Error fetching generation data for {'name': 'Kids Clinic India Limited', 'plant_id': 'IN.INTE.KIDS'}: 'str' object has no attribute 'empty'
2025-06-03 16:01:23 - ERROR - error_only - error_logger.py:66 - [data] Traceback (most recent call last):
  File "D:\Harikrishnan\Plot Generation\backend\data\data.py", line 883, in get_generation_only_data
    if df is None or df.empty:
                     ^^^^^^^^
AttributeError: 'str' object has no attribute 'empty'

2025-06-03 16:02:12 - ERROR - error_only - error_logger.py:66 - [data] Failed to retrieve generation data from API: 'str' object has no attribute 'empty'
2025-06-03 16:02:12 - ERROR - error_only - error_logger.py:66 - [data] Traceback (most recent call last):
  File "D:\Harikrishnan\Plot Generation\backend\data\data.py", line 1542, in get_generation_data
    if not df.empty:
           ^^^^^^^^
AttributeError: 'str' object has no attribute 'empty'

2025-06-03 16:02:59 - ERROR - error_only - error_logger.py:66 - [data] Failed to retrieve hourly generation data from API: 'str' object has no attribute 'empty'
2025-06-03 16:02:59 - ERROR - error_only - error_logger.py:66 - [data] Traceback (most recent call last):
  File "D:\Harikrishnan\Plot Generation\backend\data\data.py", line 1671, in get_hourly_generation_data
    if not df.empty:
           ^^^^^^^^
AttributeError: 'str' object has no attribute 'empty'

2025-06-03 16:03:41 - ERROR - error_only - error_logger.py:66 - [data] Failed to retrieve hourly generation data from API: 'str' object has no attribute 'empty'
2025-06-03 16:03:41 - ERROR - error_only - error_logger.py:66 - [data] Traceback (most recent call last):
  File "D:\Harikrishnan\Plot Generation\backend\data\data.py", line 1671, in get_hourly_generation_data
    if not df.empty:
           ^^^^^^^^
AttributeError: 'str' object has no attribute 'empty'

2025-06-03 16:04:25 - ERROR - error_only - error_logger.py:66 - [data] Failed to retrieve hourly generation data from API: 'str' object has no attribute 'empty'
2025-06-03 16:04:25 - ERROR - error_only - error_logger.py:66 - [data] Traceback (most recent call last):
  File "D:\Harikrishnan\Plot Generation\backend\data\data.py", line 1671, in get_hourly_generation_data
    if not df.empty:
           ^^^^^^^^
AttributeError: 'str' object has no attribute 'empty'

2025-06-03 16:05:09 - ERROR - error_only - error_logger.py:66 - [data] Failed to retrieve hourly generation data from API: 'str' object has no attribute 'empty'
2025-06-03 16:05:09 - ERROR - error_only - error_logger.py:66 - [data] Traceback (most recent call last):
  File "D:\Harikrishnan\Plot Generation\backend\data\data.py", line 1671, in get_hourly_generation_data
    if not df.empty:
           ^^^^^^^^
AttributeError: 'str' object has no attribute 'empty'

2025-06-03 16:05:52 - ERROR - error_only - error_logger.py:66 - [data] Failed to retrieve hourly generation data from API: 'str' object has no attribute 'empty'
2025-06-03 16:05:52 - ERROR - error_only - error_logger.py:66 - [data] Traceback (most recent call last):
  File "D:\Harikrishnan\Plot Generation\backend\data\data.py", line 1671, in get_hourly_generation_data
    if not df.empty:
           ^^^^^^^^
AttributeError: 'str' object has no attribute 'empty'

2025-06-03 16:06:38 - ERROR - error_only - error_logger.py:66 - [data] Failed to retrieve hourly generation data from API: 'str' object has no attribute 'empty'
2025-06-03 16:06:38 - ERROR - error_only - error_logger.py:66 - [data] Traceback (most recent call last):
  File "D:\Harikrishnan\Plot Generation\backend\data\data.py", line 1671, in get_hourly_generation_data
    if not df.empty:
           ^^^^^^^^
AttributeError: 'str' object has no attribute 'empty'

2025-06-03 16:07:24 - ERROR - error_only - error_logger.py:66 - [data] Failed to retrieve hourly generation data from API: 'str' object has no attribute 'empty'
2025-06-03 16:07:24 - ERROR - error_only - error_logger.py:66 - [data] Traceback (most recent call last):
  File "D:\Harikrishnan\Plot Generation\backend\data\data.py", line 1671, in get_hourly_generation_data
    if not df.empty:
           ^^^^^^^^
AttributeError: 'str' object has no attribute 'empty'

2025-06-03 16:08:07 - ERROR - error_only - error_logger.py:66 - [data] Failed to retrieve hourly generation data from API: 'str' object has no attribute 'empty'
2025-06-03 16:08:07 - ERROR - error_only - error_logger.py:66 - [data] Traceback (most recent call last):
  File "D:\Harikrishnan\Plot Generation\backend\data\data.py", line 1671, in get_hourly_generation_data
    if not df.empty:
           ^^^^^^^^
AttributeError: 'str' object has no attribute 'empty'

2025-06-03 16:08:52 - ERROR - error_only - error_logger.py:66 - [data] Failed to retrieve hourly generation data from API: 'str' object has no attribute 'empty'
2025-06-03 16:08:52 - ERROR - error_only - error_logger.py:66 - [data] Traceback (most recent call last):
  File "D:\Harikrishnan\Plot Generation\backend\data\data.py", line 1671, in get_hourly_generation_data
    if not df.empty:
           ^^^^^^^^
AttributeError: 'str' object has no attribute 'empty'

2025-06-04 18:12:00 - ERROR - error_only - error_logger.py:66 - [centralized_data_wrapper] Error ensuring data is loaded: unhashable type: 'dict'
2025-06-04 18:12:00 - ERROR - error_only - error_logger.py:66 - [centralized_data_wrapper] Error ensuring data is loaded: unhashable type: 'dict'
2025-06-04 18:12:04 - ERROR - error_only - error_logger.py:66 - [centralized_data_wrapper] Error ensuring data is loaded: unhashable type: 'dict'
2025-06-04 18:12:04 - ERROR - error_only - error_logger.py:66 - [centralized_data_wrapper] Error ensuring data is loaded: unhashable type: 'dict'
2025-06-04 18:12:05 - ERROR - error_only - error_logger.py:66 - [centralized_data_wrapper] Error ensuring data is loaded: unhashable type: 'dict'
2025-06-04 18:12:05 - ERROR - error_only - error_logger.py:66 - [centralized_data_wrapper] Error ensuring data is loaded: unhashable type: 'dict'
2025-06-04 18:12:09 - ERROR - error_only - error_logger.py:66 - [centralized_data_wrapper] Error ensuring data is loaded: unhashable type: 'dict'
2025-06-04 18:12:09 - ERROR - error_only - error_logger.py:66 - [centralized_data_wrapper] Error ensuring data is loaded: unhashable type: 'dict'
2025-06-04 18:12:09 - ERROR - error_only - error_logger.py:66 - [centralized_data_wrapper] Error ensuring data is loaded: unhashable type: 'dict'
2025-06-04 18:12:39 - ERROR - error_only - error_logger.py:66 - [centralized_data_wrapper] Error ensuring data is loaded: unhashable type: 'dict'
2025-06-04 18:12:39 - ERROR - error_only - error_logger.py:66 - [centralized_data_wrapper] Error ensuring data is loaded: unhashable type: 'dict'
2025-06-04 18:12:39 - ERROR - error_only - error_logger.py:66 - [centralized_data_wrapper] Error ensuring data is loaded: unhashable type: 'dict'
2025-06-04 18:12:39 - ERROR - error_only - error_logger.py:66 - [centralized_data_wrapper] Error ensuring data is loaded: unhashable type: 'dict'
2025-06-04 18:13:15 - ERROR - error_only - error_logger.py:66 - [centralized_data_wrapper] Error ensuring data is loaded: unhashable type: 'dict'
2025-06-04 18:13:15 - ERROR - error_only - error_logger.py:66 - [centralized_data_wrapper] Error ensuring data is loaded: unhashable type: 'dict'
2025-06-04 18:13:15 - ERROR - error_only - error_logger.py:66 - [centralized_data_wrapper] Error ensuring data is loaded: unhashable type: 'dict'
2025-06-04 18:13:15 - ERROR - error_only - error_logger.py:66 - [centralized_data_wrapper] Error ensuring data is loaded: unhashable type: 'dict'
2025-06-04 18:13:15 - ERROR - error_only - error_logger.py:66 - [centralized_data_wrapper] Error ensuring data is loaded: unhashable type: 'dict'
2025-06-04 18:13:29 - ERROR - error_only - error_logger.py:66 - [centralized_data_wrapper] Error ensuring data is loaded: unhashable type: 'dict'
2025-06-04 18:13:29 - ERROR - error_only - error_logger.py:66 - [centralized_data_wrapper] Error ensuring data is loaded: unhashable type: 'dict'
2025-06-04 18:13:29 - ERROR - error_only - error_logger.py:66 - [centralized_data_wrapper] Error ensuring data is loaded: unhashable type: 'dict'
2025-06-04 18:13:29 - ERROR - error_only - error_logger.py:66 - [centralized_data_wrapper] Error ensuring data is loaded: unhashable type: 'dict'
2025-06-04 18:13:29 - ERROR - error_only - error_logger.py:66 - [centralized_data_wrapper] Error ensuring data is loaded: unhashable type: 'dict'
2025-06-04 18:13:29 - ERROR - error_only - error_logger.py:66 - [centralized_data_wrapper] Error ensuring data is loaded: unhashable type: 'dict'
2025-06-04 18:13:49 - ERROR - error_only - error_logger.py:66 - [centralized_data_wrapper] Error ensuring data is loaded: unhashable type: 'dict'
2025-06-04 18:13:49 - ERROR - error_only - error_logger.py:66 - [centralized_data_wrapper] Error ensuring data is loaded: unhashable type: 'dict'
2025-06-04 18:13:49 - ERROR - error_only - error_logger.py:66 - [centralized_data_wrapper] Error ensuring data is loaded: unhashable type: 'dict'
2025-06-04 18:13:49 - ERROR - error_only - error_logger.py:66 - [centralized_data_wrapper] Error ensuring data is loaded: unhashable type: 'dict'
2025-06-04 18:13:49 - ERROR - error_only - error_logger.py:66 - [centralized_data_wrapper] Error ensuring data is loaded: unhashable type: 'dict'
2025-06-04 18:13:49 - ERROR - error_only - error_logger.py:66 - [centralized_data_wrapper] Error ensuring data is loaded: unhashable type: 'dict'
